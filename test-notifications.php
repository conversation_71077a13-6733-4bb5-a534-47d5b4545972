<?php

require_once 'vendor/autoload.php';

use Illuminate\Foundation\Application;
use Illuminate\Contracts\Console\Kernel;

// Bootstrap Laravel
$app = require_once 'bootstrap/app.php';
$kernel = $app->make(Kernel::class);
$kernel->bootstrap();

echo "🧪 Testing Notification System...\n\n";

// Test 1: Check if Reverb is configured
echo "1. Checking Reverb configuration...\n";
$broadcastConnection = env('BROADCAST_CONNECTION');
$reverbKey = env('REVERB_APP_KEY');

if ($broadcastConnection === 'reverb' && !empty($reverbKey)) {
    echo "   ✅ Reverb is properly configured\n";
} else {
    echo "   ❌ Reverb configuration issue\n";
    echo "   - BROADCAST_CONNECTION: $broadcastConnection\n";
    echo "   - REVERB_APP_KEY: " . (empty($reverbKey) ? 'NOT SET' : 'SET') . "\n";
}

// Test 2: Check queue configuration
echo "\n2. Checking queue configuration...\n";
$queueConnection = env('QUEUE_CONNECTION');
echo "   Queue connection: $queueConnection\n";

if ($queueConnection === 'database') {
    echo "   ✅ Queue is configured to use database\n";
} else {
    echo "   ⚠️  Queue is not using database driver\n";
}

// Test 3: Check if notifications table exists
echo "\n3. Checking database tables...\n";
try {
    $notificationCount = \Illuminate\Support\Facades\DB::table('notifications')->count();
    echo "   ✅ Notifications table exists ($notificationCount notifications)\n";
} catch (Exception $e) {
    echo "   ❌ Notifications table issue: " . $e->getMessage() . "\n";
}

try {
    $jobsCount = \Illuminate\Support\Facades\DB::table('jobs')->count();
    echo "   ✅ Jobs table exists ($jobsCount pending jobs)\n";
} catch (Exception $e) {
    echo "   ❌ Jobs table issue: " . $e->getMessage() . "\n";
}

// Test 4: Check if events are properly configured
echo "\n4. Checking event configuration...\n";
$eventServiceProvider = new \App\Providers\EventServiceProvider(app());
$listeners = $eventServiceProvider->listens();

$expectedEvents = [
    'App\Events\PostReactionAdded',
    'App\Events\PostCommentAdded', 
    'App\Events\PostSharedEvent'
];

foreach ($expectedEvents as $event) {
    if (isset($listeners[$event])) {
        echo "   ✅ $event is configured\n";
    } else {
        echo "   ❌ $event is not configured\n";
    }
}

echo "\n🎯 Test Summary:\n";
echo "- Run 'start-notifications.bat' to start all services\n";
echo "- Access your app at http://localhost:8000\n";
echo "- Test by reacting to posts or commenting\n";
echo "- Check browser console for WebSocket connection status\n";
echo "\n";
