<?php

namespace App\Notifications;

use App\Models\User;
use App\Models\Group;
use App\Models\Post;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\BroadcastMessage;
use Illuminate\Notifications\Notification;

class GroupPostRejected extends Notification implements ShouldQueue
{
    use Queueable;

    public $post;
    public $group;
    public $rejector;

    /**
     * Create a new notification instance.
     */
    public function __construct(Post $post, Group $group, User $rejector)
    {
        $this->post = $post;
        $this->group = $group;
        $this->rejector = $rejector;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @return array<int, string>
     */
    public function via(object $notifiable): array
    {
        return ['database', 'broadcast'];
    }

    /**
     * Get the broadcastable representation of the notification.
     */
    public function toBroadcast(object $notifiable): BroadcastMessage
    {
        return new BroadcastMessage([
            'id' => $this->id,
            'type' => 'group_post_rejected',
            'rejector_id' => $this->rejector->id,
            'rejector_name' => $this->rejector->name,
            'rejector_avatar' => $this->rejector->getNotificationAvatarUrl(),
            'post_id' => $this->post->id,
            'post_title' => $this->post->title,
            'group_id' => $this->group->id,
            'group_name' => $this->group->name,
            'group_logo' => $this->group->logo ? \Illuminate\Support\Facades\Storage::disk('public')->url($this->group->logo) : null,
            'message' => $this->getMessage(),
            'url' => $this->getGroupUrl(),
            'created_at' => now()->toISOString(),
        ]);
    }

    /**
     * Get the array representation of the notification.
     *
     * @return array<string, mixed>
     */
    public function toArray(object $notifiable): array
    {
        return [
            'type' => 'group_post_rejected',
            'rejector_id' => $this->rejector->id,
            'rejector_name' => $this->rejector->name,
            'rejector_avatar' => $this->rejector->getNotificationAvatarUrl(),
            'post_id' => $this->post->id,
            'post_title' => $this->post->title,
            'group_id' => $this->group->id,
            'group_name' => $this->group->name,
            'group_logo' => $this->group->logo ? \Illuminate\Support\Facades\Storage::disk('public')->url($this->group->logo) : null,
            'message' => $this->getMessage(),
            'url' => $this->getGroupUrl(),
        ];
    }

    /**
     * Get the notification message
     */
    private function getMessage(): string
    {
        return "Your post \"{$this->post->title}\" was rejected in {$this->group->name}";
    }

    /**
     * Get the group URL
     */
    private function getGroupUrl(): string
    {
        return route('groups.show', $this->group->slug);
    }
}
